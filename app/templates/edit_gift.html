{% extends "base.html" %}

{% block title %}{{ _('Edit Gift') }} - {{ gift.name }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}">{{ gift.gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Edit Gift') }}</li>
{% endblock %}

{% block content %}
<div class="container-xl py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">{{ _('Edit Gift') }}</h1>
        <a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i> {{ _('Back to List') }}
        </a>
    </div>
    <div class="card shadow-sm rounded-3">
        <div class="card-header bg-transparent rounded-top-3">
            <h5 class="mb-0"><i class="bi bi-pencil-square me-2"></i>{{ _('Edit Gift Details') }}</h5>
        </div>
        <div class="card-body p-4">
            <form method="POST" action="{{ url_for('main.edit_gift', gift_id=gift.id) }}" id="editGiftForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="mb-4">
                    <label for="name" class="form-label">{{ _('Gift Name:') }}</label>
                    <div class="input-group mb-2">
                        <span class="input-group-text"><i class="bi bi-tag"></i></span>
                        <input type="text" class="form-control form-control-lg" id="name" name="name"
                            value="{{ gift.name }}" required>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="link" class="form-label">{{ _('Gift Link:') }}</label>
                    <div class="input-group mb-2">
                        <span class="input-group-text"><i class="bi bi-link"></i></span>
                        <input type="url" class="form-control form-control-lg" id="link" name="link" value="{{ gift.link }}" required>
                    </div>
                    <div class="form-text" id="linkHelpText">{{ _('The URL where this gift can be purchased') }}</div>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitButton">
                        <i class="bi bi-check-lg me-1"></i>{{ _('Save Changes') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}{{ super() }}{% endblock %}
