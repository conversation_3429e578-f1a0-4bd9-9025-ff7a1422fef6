{% extends "base.html" %}

{% block title %}{{ _('Register') }} - FiestaMagic{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ _('Register') }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-person-plus me-2"></i>{{ _('Create an Account') }}</h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('auth.register') }}">
                    {{ form.csrf_token }}
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ _('Email Address') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            {{ form.email(class="form-control", id="email") }}
                        </div>
                        <div class="form-text">{{ _('We\'ll never share your email with anyone else.') }}</div>
                        {% for error in form.email.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">{{ _('Username') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person"></i></span>
                            {{ form.username(class="form-control", id="username") }}
                        </div>
                        <div class="form-text">{{ _('Choose a unique username.') }}</div>
                        {% for error in form.username.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">{{ _('Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock"></i></span>
                            {{ form.password(class="form-control", id="password") }}
                        </div>
                        {% for error in form.password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ _('Confirm Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                            {{ form.confirm_password(class="form-control", id="confirm_password") }}
                        </div>
                        {% for error in form.confirm_password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="theme" class="form-label">{{ _('Preferred Theme') }}</label>
                        {{ form.theme(class="form-select", id="theme") }}
                        {% for error in form.theme.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="language" class="form-label">{{ _('Preferred Language') }}</label>
                        {{ form.language(class="form-select", id="language") }}
                        {% for error in form.language.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="bi bi-person-plus me-1"></i> {{ _('Register') }}
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p>{{ _('Already have an account?') }} <a href="{{ url_for('auth.login') }}">{{ _('Login') }}</a>
                    </p>
                    <p>{{ _('Or sign in with') }}</p>
                    <a href="{{ url_for('google.login') }}" class="btn btn-sm btn-outline-danger">
                        <i class="bi bi-google me-1"></i> {{ _('Sign in with Google') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
