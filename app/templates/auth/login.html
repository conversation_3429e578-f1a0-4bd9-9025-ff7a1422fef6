{% extends "base.html" %}

{% block title %}{{ _('Login') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ _('Login') }}</li>
{% endblock %}

{% block content %}
<div class="py-4">
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap gap-2">
        <h1 class="mb-0">{{ _('Login') }}</h1>
        <a href="{{ url_for('main.index') }}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i> {{ _('Back to Home') }}
        </a>
    </div>
    <form method="POST" action="{{ url_for('auth.login') }}" class="mx-auto" style="max-width:400px;">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="mb-4">
            <label for="email" class="form-label">{{ _('Email address') }}</label>
            <input type="email" class="form-control" id="email" name="email"
                value="{{ request.form.email or '' }}" required autofocus>
        </div>
        <div class="mb-4">
            <label for="password" class="form-label">{{ _('Password') }}</label>
            <input type="password" class="form-control" id="password" name="password" required>
            </div>
        <div class="mb-3 d-flex justify-content-between align-items-center">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                <label class="form-check-label" for="remember">{{ _('Remember me') }}</label>
            </div>
            <a href="{{ url_for('auth.reset_password_request') }}" class="small">{{ _('Forgot password?') }}</a>
            </div>
        <div class="d-grid gap-2 mt-4">
            <button type="submit" class="btn btn-sm btn-primary">
                <i class="bi bi-box-arrow-in-right me-1"></i> {{ _('Login') }}
            </button>
        </div>
        <div class="text-center mt-4">
            <span class="text-muted small">{{ _('or') }}</span>
        </div>
        <div class="d-grid gap-2 mt-3">
            <a href="{{ url_for('google.login') }}" class="btn btn-sm btn-outline-danger">
                <i class="bi bi-google me-1"></i> {{ _('Sign in with Google') }}
            </a>
        </div>
        <div class="text-center mt-4">
            <span class="text-muted small">{{ _('Don\'t have an account?') }}</span>
            <a href="{{ url_for('auth.register') }}" class="small ms-1">{{ _('Register') }}</a>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}{{ super() }}{% endblock %}