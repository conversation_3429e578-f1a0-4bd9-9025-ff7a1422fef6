{% extends "base.html" %}

{% block title %}{{ _('Reset Password') }} - FiestaMagic{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ _('Reset Password') }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-key me-2"></i>{{ _('Reset Password') }}</h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted mb-4">{{ _('Enter your email address and we will send you a link to reset your password.') }}</p>
                
                <form method="POST" action="{{ url_for('auth.reset_password_request') }}">
                    {{ form.csrf_token }}
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ _('Email Address') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            {{ form.email(class="form-control", id="email") }}
                        </div>
                        {% for error in form.email.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-envelope me-1"></i> {{ _('Request Password Reset') }}
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p><a href="{{ url_for('auth.login') }}">{{ _('Back to Login') }}</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
