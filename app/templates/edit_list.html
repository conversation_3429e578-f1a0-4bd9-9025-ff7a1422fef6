{% extends "base.html" %}

{% block title %}{{ _('Edit List') }} - {{ gift_list.name }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift_list.id) }}">{{ gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Edit') }}</li>
{% endblock %}

{% block content %}
<div class="py-4">
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap gap-2">
        <h1 class="mb-0">{{ _('Edit Gift List') }}</h1>
        <a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i> {{ _('Back to List') }}
        </a>
    </div>
    <form method="POST" action="{{ url_for('main.edit_list', list_id=gift_list.unique_id) }}" class="mx-auto" style="max-width:600px;">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="mb-4">
            <label for="name" class="form-label">{{ _('List Name:') }}</label>
            <input type="text" class="form-control form-control-lg" id="name" name="name" value="{{ gift_list.name }}" required>
            <div class="form-text">{{ _('Give your list a meaningful name like "Birthday Wishlist" or "Wedding Registry"') }}</div>
        </div>
        <div class="mb-4">
            <label class="form-label">{{ _('Privacy Setting:') }}</label>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="is_private" id="privateList" value="true" {% if gift_list.is_private %}checked{% endif %}>
                <label class="form-check-label" for="privateList">
                    <i class="bi bi-lock-fill text-danger me-1"></i>{{ _('Private') }}
                    <div class="form-text">{{ _('Only you and people with the share link can view this list') }}</div>
                </label>
            </div>
            <div class="form-check mt-2">
                <input class="form-check-input" type="radio" name="is_private" id="publicList" value="false" {% if not gift_list.is_private %}checked{% endif %}>
                <label class="form-check-label" for="publicList">
                    <i class="bi bi-unlock-fill text-success me-1"></i>{{ _('Public') }}
                    <div class="form-text">{{ _('Anyone can view this list without requiring a share link') }}</div>
                </label>
            </div>
        </div>
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
            <a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}" class="btn btn-outline-secondary">
                <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-lg me-1"></i>{{ _('Save Changes') }}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}{{ super() }}{% endblock %}