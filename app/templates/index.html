{% extends "base.html" %}
{% block title %}{{ _('Home') }}{% endblock %}

{% block breadcrumbs %}
{# Empty breadcrumbs block to prevent "Home / Home" duplication #}
{% endblock %}

{% block content %}
{% if current_user.is_authenticated %}
<!-- Dashboard for logged-in users -->
<div class="container-xl py-5">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="fw-bold mb-0">{{ _('My Gift Lists') }}</h1>
        </div>
        <div class="col-md-6 text-md-end">
            <button class="btn btn-3 btn-primary" data-bs-toggle="modal" data-bs-target="#addListModal">
                <i class="bi bi-plus-circle me-2"></i>{{ _('Create New List') }}
            </button>
        </div>
    </div>

    {% if lists %}
    <div class="row g-4">
        {% for list in lists %}
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0 fw-bold">
                            <a href="{{ url_for('main.gift_list_view', list_id=list.unique_id) }}"
                                class="stretched-link text-primary text-decoration-none">
                                {{ list.name }}
                            </a>
                        </h5>
                        <span class="badge rounded-pill {% if list.is_private %}bg-danger{% else %}bg-success{% endif %}">
                            {% if list.is_private %}
                            <i class="bi bi-lock-fill me-1"></i>{{ _('Private') }}
                            {% else %}
                            <i class="bi bi-unlock-fill me-1"></i>{{ _('Public') }}
                            {% endif %}
                        </span>
                    </div>
                    <p class="card-text text-muted small mb-3">
                        <i class="bi bi-gift me-1"></i>{{ _('%(count)d gifts', count=list.gifts.count()) }}
                        <span class="mx-2">•</span>
                        <i class="bi bi-calendar3 me-1"></i>{{ _('Created %(date)s', date=format_date(list.created_at)) }}
                    </p>
                    <div class="d-flex justify-content-start">
                        <a href="{{ url_for('main.gift_list_view', list_id=list.unique_id) }}" class="btn btn-3 btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>{{ _('View') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <img src="{{ url_for('static', filename='images/empty-list.svg') }}" alt="No lists" class="img-fluid" style="max-height: 200px;">
        </div>
        <h3 class="fw-bold mb-3">{{ _('No Gift Lists Yet') }}</h3>
        <p class="text-muted mb-4">{{ _('Create your first gift list to get started.') }}</p>
        <button class="btn btn-3 btn-primary" data-bs-toggle="modal" data-bs-target="#addListModal">
            <i class="bi bi-plus-circle me-2"></i>{{ _('Create Your First List') }}
        </button>
    </div>
    {% endif %}
</div>
{% else %}
<!-- Landing page for non-logged-in users -->
<section class="py-5 bg-white rounded-3 shadow-sm">
    <div class="container-xl">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <h1 class="display-4 fw-bold mb-3">{{ _('Simplify Gift Giving with FiestaMagic') }}</h1>
                <p class="lead mb-4">{{ _("Create, share, and manage gift lists for any occasion. Never worry about duplicate gifts or forgotten wishes again.") }}</p>
                <div class="mb-4">
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary hover-lift me-2">
                        <i class="bi bi-person-plus me-1"></i> {{ _('Get Started - Free') }}
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary hover-lift">
                        <i class="bi bi-box-arrow-in-right me-1"></i> {{ _('Login') }}
                    </a>
                </div>
                <p class="text-muted small">{{ _('No credit card required. Free forever.') }}</p>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-image-container">
                    <img src="{{ url_for('static', filename='images/hero-image.svg') }}" alt="Gift Lists"
                        class="img-fluid" style="max-height: 400px;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center fw-bold mb-5">{{ _('How FiestaMagic Works') }}</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4"
                            style="width: 80px; height: 80px;">
                            <span class="h3 fw-bold mb-0">1</span>
                        </div>
                        <h3 class="h5 fw-bold">{{ _('Create Your Gift Lists') }}</h3>
                        <p class="text-muted">{{ _('Add items with names and links. Organize them by occasion or recipient.') }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4"
                            style="width: 80px; height: 80px;">
                            <span class="h3 fw-bold mb-0">2</span>
                        </div>
                        <h3 class="h5 fw-bold">{{ _('Share with Friends & Family') }}</h3>
                        <p class="text-muted">{{ _('Share your lists via link, QR code, or keep them private with a share code.') }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4"
                            style="width: 80px; height: 80px;">
                            <span class="h3 fw-bold mb-0">3</span>
                        </div>
                        <h3 class="h5 fw-bold">{{ _('Enjoy Stress-Free Gift Giving') }}</h3>
                        <p class="text-muted">{{ _('Friends can reserve items to avoid duplicates while keeping the surprise.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white text-center">
    <div class="container">
        <h2 class="fw-bold mb-3">{{ _('Ready to Simplify Gift Giving?') }}</h2>
        <p class="lead mb-4">{{ _('Join thousands of users who have made gift giving easier with FiestaMagic.') }}</p>
        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg px-5 hover-lift">
            <i class="bi bi-person-plus me-2"></i> {{ _('Create Your Free Account') }}
        </a>
    </div>
</section>
{% endif %}

<!-- Add List Modal -->
{% if current_user.is_authenticated %}
<div class="modal fade" id="addListModal" tabindex="-1" aria-labelledby="addListModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addListModalLabel">
                    <i class="bi bi-list-check me-2"></i>{{ _('Create New List') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="mb-4">
                    <label for="name" class="form-label">{{ _('List Name:') }}</label>
                    <input type="text" class="form-control" id="listName" placeholder="{{ _('Enter a descriptive name') }}"
                        required>
                    <div class="form-text">{{ _('Give your list a meaningful name like "Birthday Wishlist" or "Wedding Registry"') }}</div>
                </div>
                <div class="mb-4">
                    <label class="form-label">{{ _('Privacy Setting:') }}</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="privacy" id="privateList" value="private" checked>
                        <label class="form-check-label" for="privateList">
                            <i class="bi bi-lock-fill text-danger me-1"></i>{{ _('Private') }}
                            <div class="form-text">{{ _('Only you and people with the share link can view this list') }}</div>
                        </label>
                    </div>
                    <div class="form-check mt-2">
                        <input class="form-check-input" type="radio" name="privacy" id="publicList" value="public">
                        <label class="form-check-label" for="publicList">
                            <i class="bi bi-unlock-fill text-success me-1"></i>{{ _('Public') }}
                            <div class="form-text">{{ _('Anyone can view this list without requiring a share link') }}</div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                </button>
                <button type="button" class="btn btn-3 btn-primary" onclick="createList(false)">
                    <i class="bi bi-check-lg me-1"></i>{{ _('Create') }}
                </button>
                <button type="button" class="btn btn-3 btn-success" onclick="createList(true)">
                    <i class="bi bi-box-arrow-up-right me-1"></i>{{ _('Create & Open') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for List Deletion -->
<div class="modal fade" id="confirmDeleteListModal" tabindex="-1" aria-labelledby="confirmDeleteListModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteListModalLabel">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>{{ _('Confirm Delete') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <p class="mb-1">{{ _('Are you sure you want to delete the list:') }}</p>
                <p class="fs-5 fw-bold mb-3"><span id="listName"></span>?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ _("This action cannot be undone. All gifts in this list will be permanently deleted.") }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                </button>
                <form id="confirmDeleteListForm" method="POST" action="#">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-3 btn-danger">
                        <i class="bi bi-trash me-1"></i>{{ _('Delete List') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
            document.addEventListener('DOMContentLoaded', function () {
                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                })
            });

            {% if current_user.is_authenticated %}
            // Handle delete modal
            var confirmDeleteListModal = document.getElementById('confirmDeleteListModal');
            confirmDeleteListModal.addEventListener('show.bs.modal', function (event) {
                var button = event.relatedTarget;
                var listId = button.getAttribute('data-id');
                var listName = button.getAttribute('data-name');
        var deleteUrl = "{{ url_for('main.delete_list', list_id=0) }}".replace('0', listId);

            this.querySelector('#confirmDeleteListForm').setAttribute('action', deleteUrl);
            this.querySelector('#listName').textContent = listName;
    });

    function createList(openAfterCreate) {
        const name = document.getElementById('listName').value;
        if (!name) {
            // Show validation error with better UX
            const input = document.getElementById('listName');
            input.classList.add('is-invalid');

            // Add validation message if it doesn't exist
            if (!document.querySelector('.invalid-feedback')) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = '{{ _('Please enter a list name') }}';
                input.parentNode.appendChild(feedback);
            }

            // Focus the input
            input.focus();
            return;
        }

        // Show loading state on buttons
        const buttons = document.querySelectorAll('#addListModal .modal-footer button');
        buttons.forEach(button => {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{{ _('Loading...') }}';
        });

        const formData = new FormData();
        formData.append('name', name);
        formData.append('redirect', openAfterCreate ? '1' : '0');

                    // Get privacy setting
                    const isPrivate = document.getElementById('privateList').checked;
                    formData.append('is_private', isPrivate ? 'true' : 'false');

                    // Add CSRF token
                    formData.append('csrf_token', '{{ csrf_token() }}');

        fetch("{{ url_for('main.add_list') }}", {
            method: 'POST',
            body: formData
        })
            .then(response => {
                if (openAfterCreate && response.redirected) {
                    window.location.href = response.url;
                } else {
                    window.location.href = "{{ url_for('main.index') }}";
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Restore button state in case of error
                buttons.forEach(button => {
                    button.disabled = false;
                    button.innerHTML = button.getAttribute('data-original-text');
                });

                // Show error message
                alert('{{ _("An error occurred. Please try again.") }}');
            });

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addListModal'));
        modal.hide();
    }

    // Remove validation styling when user starts typing
    document.getElementById('listName').addEventListener('input', function () {
        this.classList.remove('is-invalid');
    });
            {% endif %}
</script>
{% endblock %}
