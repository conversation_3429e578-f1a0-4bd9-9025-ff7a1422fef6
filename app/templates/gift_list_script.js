document.addEventListener('DOMContentLoaded', function () {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover' // Only show tooltips on hover, not on tap (better for mobile)
        })
    })

    // Handle anonymous reservation with modal and AJAX form submission
    const reserveForms = document.querySelectorAll('.reservation-form');
    const reservationModal = new bootstrap.Modal(document.getElementById('reservationModal'));
    const anonymousReservationForm = document.getElementById('anonymousReservationForm');

    // Set up event listeners for each reservation form
    reserveForms.forEach(form => {
        const reserveButton = form.querySelector('button[type="submit"]');

        // For anonymous users, show modal on reserve button click
        if (reserveButton && reserveButton.hasAttribute('data-gift-id')) {
            reserveButton.addEventListener('click', function (e) {
                // Only for non-reserved gifts and anonymous users
                if (!reserveButton.classList.contains('btn-outline-success')) {
                    e.preventDefault();

                    // Set the gift ID in the modal form
                    const giftId = reserveButton.getAttribute('data-gift-id');
                    document.getElementById('reservationGiftId').value = giftId;

                    // Set the form action to the correct endpoint
                    anonymousReservationForm.action = form.action;

                    // Show the modal
                    reservationModal.show();

                    // Focus the name input
                    setTimeout(() => {
                        document.getElementById('reserverName').focus();
                    }, 500);
                }
            });
        }

        // Handle AJAX form submission for regular reservation forms
        form.addEventListener('submit', function (e) {
            // Skip if this is for an anonymous user (handled by the modal)
            if (reserveButton && reserveButton.hasAttribute('data-gift-id') && 
                !reserveButton.classList.contains('btn-outline-success')) {
                return; // This will be handled by the modal form
            }
            
            e.preventDefault();
            submitReservationForm(form);
        });
    });
    
    // Handle the anonymous reservation modal form submission
    if (anonymousReservationForm) {
        anonymousReservationForm.addEventListener('submit', function (e) {
            e.preventDefault();
            
            // Find the original form that triggered this modal
            const giftId = document.getElementById('reservationGiftId').value;
            const originalButton = document.querySelector(`button[data-gift-id="${giftId}"]`);
            const originalForm = originalButton ? originalButton.closest('form') : null;
            
            // Hide the modal
            reservationModal.hide();
            
            // Submit the form with the name from the modal
            submitReservationForm(anonymousReservationForm, originalForm);
        });
    }
});

// Function to handle form submission via AJAX
function submitReservationForm(form, originalForm = null) {
    // Determine which form to update UI for after submission
    const uiForm = originalForm || form;
    
    // Show loading state
    const submitButton = uiForm.querySelector('button[type="submit"]');
    let originalHtml = '';
    if (submitButton) {
        originalHtml = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
    }

    // Get form data
    const formData = new FormData(form);

    // Send AJAX request
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Show toast message
        showToast(data.message, data.category);

        // Update UI based on reservation status
        updateGiftReservation(uiForm, data.reserved);

        // Restore button state
        if (submitButton) {
            submitButton.disabled = false;
            if (data.reserved) {
                submitButton.innerHTML = '<i class="bi bi-bookmark-check-fill"></i>';
                submitButton.className = 'btn btn-sm btn-outline-success';
                submitButton.setAttribute('data-bs-title', '{{ _('Unreserve') }}');
            } else {
                submitButton.innerHTML = '<i class="bi bi-bookmark"></i>';
                submitButton.className = 'btn btn-sm btn-outline-warning';
                submitButton.setAttribute('data-bs-title', '{{ _('Reserve') }}');
            }

            // Reinitialize tooltip
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                const tooltip = bootstrap.Tooltip.getInstance(submitButton);
                if (tooltip) tooltip.dispose();
                new bootstrap.Tooltip(submitButton);
            }
        }
        
        // Reset the modal form
        if (form === anonymousReservationForm) {
            form.reset();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('{{ _('An error occurred. Please try again.') }}', 'danger');

        // Restore button state
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = originalHtml || '<i class="bi bi-bookmark"></i>';
        }
    });
}

// Function to update gift reservation UI
function updateGiftReservation(form, isReserved) {
    const giftItem = form.closest('.list-group-item');
    if (!giftItem) return;

    // Update list item class
    if (isReserved) {
        giftItem.classList.add('list-group-item-success');
    } else {
        giftItem.classList.remove('list-group-item-success');
    }

    // Update gift title (strikethrough if reserved)
    const giftTitle = giftItem.querySelector('h5');
    if (giftTitle) {
        if (isReserved) {
            giftTitle.classList.add('text-decoration-line-through');
        } else {
            giftTitle.classList.remove('text-decoration-line-through');
        }
    }

    // Update reserved badge
    let reservedBadge = giftItem.querySelector('.badge.bg-success');
    if (isReserved) {
        if (!reservedBadge) {
            // Create badge if it doesn't exist
            const badgeContainer = giftItem.querySelector('.d-flex.align-items-center.flex-grow-1');
            if (badgeContainer) {
                reservedBadge = document.createElement('span');
                reservedBadge.className = 'badge bg-success me-2';
                reservedBadge.textContent = '{{ _('Reserved') }}';
                badgeContainer.insertBefore(reservedBadge, badgeContainer.firstChild);
            }
        }
    } else {
        // Remove badge if it exists
        if (reservedBadge) {
            reservedBadge.remove();
        }
    }
}

// Function to show toast messages
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create toast
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function () {
        toast.remove();
    });
}
