{% extends 'base.html' %}

{% block title %}{{ _('Add Gift') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}">{{ gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Add Gift') }}</li>
{% endblock %}

{% block content %}
<div class="container-xl py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm rounded-3">
                <div class="card-header bg-light rounded-top-3">
                    <h2 class="mb-0 fs-4">{{ _('Add Gift to') }} "{{ gift_list.name }}"</h2>
                </div>
                <div class="card-body p-4">
                    <form id="addGiftForm" method="POST" action="{{ url_for('main.add_gift', list_id=gift_list.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- ...existing form fields... -->
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}{{ super() }}{% endblock %}
