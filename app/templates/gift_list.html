{% extends "base.html" %}

{% block title %}{{ gift_list.name }} - FiestaMagic{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ gift_list.name }}</li>
{% endblock %}

{% block content %}
<div class="container-xl py-4">
    <div class="d-flex justify-content-between align-items-start mb-3 flex-wrap gap-2">
        <div>
            <h1 class="mb-0">{{ gift_list.name }}</h1>
            <div class="d-flex align-items-center mt-2">
                {% if gift_list.is_private %}
                <span class="badge rounded-pill bg-danger me-2"><i class="bi bi-lock-fill me-1"></i>{{ _('Private') }}</span>
                {% else %}
                <span class="badge rounded-pill bg-success me-2"><i class="bi bi-unlock-fill me-1"></i>{{ _('Public') }}</span>
                {% endif %}
                <span class="text-muted small">{{ _('Created by') }} {{ gift_list.owner.username }}</span>
                </div>
                </div>
                </div>
    <div class="d-flex flex-wrap gap-1 mb-4">
        {% if current_user.is_authenticated and current_user.id == gift_list.user_id %}
        <a href="{{ url_for('main.add_gift_page', list_id=gift_list.unique_id) }}" class="btn btn-3 btn-primary">
            <i class="bi bi-plus-circle me-1"></i> {{ _('Add Gift') }}
        </a>
        <a href="{{ url_for('main.edit_list_page', list_id=gift_list.unique_id) }}" class="btn btn-3 btn-outline-secondary">
            <i class="bi bi-pencil-square me-1"></i> {{ _('Edit List') }}
        </a>
        {% endif %}
        {% if current_user.is_authenticated and current_user.id == gift_list.user_id %}
        <form method="POST" action="{{ url_for('main.toggle_privacy', list_id=gift_list.unique_id) }}" class="d-inline">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-3 {% if gift_list.is_private %}btn-danger{% else %}btn-success{% endif %}">
                <i class="bi {% if gift_list.is_private %}bi-lock{% else %}bi-unlock{% endif %} me-1"></i>
                {% if gift_list.is_private %}{{ _('Private') }}{% else %}{{ _('Public') }}{% endif %}
            </button>
            </form>
            {% endif %}
            <button type="button" class="btn btn-3 btn-outline-primary" data-bs-toggle="modal" data-bs-target="#shareModal">
                <i class="bi bi-share me-1"></i> {{ _('Share') }}
            </button>
        {% if current_user.is_authenticated and current_user.id == gift_list.user_id %}
        <button type="button" class="btn btn-3 btn-outline-danger" data-bs-toggle="modal"
            data-bs-target="#confirmDeleteListModal" data-id="{{ gift_list.unique_id }}" data-name="{{ gift_list.name }}">
            <i class="bi bi-trash me-1"></i> {{ _('Delete List') }}
        </button>
        {% endif %}
        <a href="{{ url_for('main.index') }}" class="btn btn-3 btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i> {{ _('Back to Lists') }}
        </a>
        </div>
    {% if gift_list.gifts %}
    <div class="list-group list-group-flush list-group-lg rounded-3 overflow-hidden mb-4" style="background: #fff;">
        {% for gift in gift_list.gifts %}
        <div class="list-group-item py-3 px-4 border-0 border-bottom {% if gift.reserved %}list-group-item-success{% endif %}">
            <div class="d-flex align-items-center flex-grow-1">
                {% if gift.reserved %}
                <span class="badge bg-success me-2">{{ _('Reserved') }}{% if gift.reserved_by_id or gift.reserver_name %} <span
                        class="small">{{ _('by') }} {{ gift.reserved_by.username if gift.reserved_by_id else gift.reserver_name
                        }}</span>{% endif %}</span>
                {% endif %}
                <div>
                    <h5 class="mb-1 {% if gift.reserved %}text-decoration-line-through{% endif %}">{{ gift.name }}</h5>
                    <a href="{{ gift.link }}" target="_blank" class="small text-muted text-truncate d-inline-block"
                        style="max-width: 300px;">
                        <i class="bi bi-link-45deg"></i> {{ gift.link }}
                    </a>
                    </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <div class="btn-group">
                    <a href="{{ gift.link }}" target="_blank" class="btn btn-3 btn-outline-primary" data-bs-toggle="tooltip"
                        data-bs-title="{{ _('View gift') }}">
                        <i class="bi bi-box-arrow-up-right"></i>
                    </a>
                    {% if current_user.is_authenticated and current_user.id == gift_list.user_id %}
                    <a href="{{ url_for('main.edit_gift_page', gift_id=gift.id) }}" class="btn btn-3 btn-outline-secondary"
                        data-bs-toggle="tooltip" data-bs-title="{{ _('Edit gift') }}">
                        <i class="bi bi-pencil"></i>
                    </a>
                    {% endif %}
                    </div>
                <!-- Reservation button -->
                <form class="reservation-form" method="POST" action="{{ url_for('main.toggle_reserved', gift_id=gift.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit"
                        class="btn btn-3 {% if gift.reserved %}btn-outline-success{% else %}btn-outline-warning{% endif %}" {% if not
                        current_user.is_authenticated %}data-gift-id="{{ gift.id }}" {% endif %} data-bs-toggle="tooltip"
                        data-bs-title="{{ _('Unreserve') if gift.reserved else _('Reserve') }}">
                        <i class="bi {% if gift.reserved %}bi-bookmark-check-fill{% else %}bi-bookmark{% endif %}"></i>
                    </button>
                    </form>
                {% if current_user.is_authenticated and current_user.id == gift_list.user_id %}
                <button class="btn btn-3 btn-outline-danger" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal"
                    data-id="{{ gift.id }}" data-name="{{ gift.name }}" data-bs-tooltip="{{ _('Delete gift') }}">
                    <i class="bi bi-trash"></i>
                </button>
                {% endif %}
                </div>
                </div>
                {% endfor %}
                {% else %}
        <div class="list-group-item py-4 text-center text-muted border-0">
            {{ _('No gifts added yet!') }}
        </div>
    </div>
    {% endif %}
</div>

<!-- Confirmation Modal for Gift Deletion -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteModalLabel">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>{{ _('Confirm Delete') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <p class="mb-1">{{ _('Are you sure you want to delete the gift:') }}</p>
                <p class="fs-5 fw-bold mb-3"><span id="giftName"></span>?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>{{ _('This action cannot be undone.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                </button>
                <a id="confirmDeleteButton" href="#" class="btn btn-3 btn-danger">
                    <i class="bi bi-trash me-1"></i>{{ _('Delete Gift') }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for List Deletion -->
<div class="modal fade" id="confirmDeleteListModal" tabindex="-1" aria-labelledby="confirmDeleteListModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteListModalLabel">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>{{ _('Confirm Delete') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <p class="mb-1">{{ _('Are you sure you want to delete the list:') }}</p>
                <p class="fs-5 fw-bold mb-3"><span id="listName"></span>?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>{{ _('This action cannot be undone. All gifts in this list will be permanently
                    deleted.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                </button>
                <form id="confirmDeleteListForm" method="POST" action="#">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-3 btn-danger">
                        <i class="bi bi-trash me-1"></i>{{ _('Delete List') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">
                    <i class="bi bi-share me-2"></i>{{ _('Share Gift List') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="mb-4">
                    <label for="inviteMessage" class="form-label">{{ _('Personalized Message:') }}</label>
                    <textarea id="inviteMessage" class="form-control"
                        rows="3">{{ _('You are invited to view my gift list:') }} {{ gift_list.name }}</textarea>
                    <div class="form-text">{{ _('This message will be included when sharing') }}</div>
                </div>
                <div class="text-center mb-4">
                    <p class="text-muted mb-2">{{ _('Scan this QR code to access the list:') }}</p>
                    <img src="{{ url_for('main.qr_code', list_id=gift_list.unique_id) }}" alt="QR Code" width="150"
                        class="img-thumbnail mb-2">
                    <div class="mt-3">
                        <p class="small text-muted mb-2">{{ _('Or share this link:') }}</p>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control form-control-sm" id="shareLink" readonly
                                value="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id, _external=True) }}{% if gift_list.is_private %}?code={{ gift_list.share_code }}{% endif %}">
                            <button class="btn btn-3 btn-outline-primary copy-btn" type="button">
                                <i class="bi bi-clipboard"></i> {{ _('Copy to clipboard') }}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-center">
                    <button type="button" class="btn btn-3 btn-outline-success" onclick="printInvite()">
                        <i class="bi bi-printer me-1"></i>{{ _('Print') }}
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>{{ _('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Anonymous Reservation Modal -->
<div class="modal fade" id="reservationModal" tabindex="-1" aria-labelledby="reservationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reservationModalLabel">
                    <i class="bi bi-bookmark-plus me-2"></i>{{ _('Reserve Gift') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="anonymousReservationForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="reservationGiftId" name="gift_id" value="">
                <div class="modal-body p-4">
                    <p class="mb-3">{{ _('You are about to reserve this gift. Would you like to add your name?') }}</p>
                    <div class="mb-3">
                        <label for="reserverName" class="form-label">{{ _('Your name (optional):') }}</label>
                        <input type="text" class="form-control" id="reserverName" name="reserver_name"
                            placeholder="{{ _('Enter your name') }}" maxlength="100">
                        <div class="form-text">{{ _('This helps the list owner know who reserved the gift') }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-3 btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-3 btn-primary">
                        <i class="bi bi-bookmark-check me-1"></i>{{ _('Reserve') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Translation strings for JavaScript
    const reserveText = "{{ _('Reserve') }}";
    const unreserveText = "{{ _('Unreserve') }}";
    const reservedText = "{{ _('Reserved') }}";
    const errorText = "{{ _('An error occurred. Please try again.') }}";

    // Translation strings for clipboard.js
    const copiedTranslation = "{{ _('Copied!') }}";
    const linkCopiedTranslation = "{{ _('Link copied to clipboard!') }}";
    const copyFailedText = "{{ _('Failed to copy link. Please try again.') }}";
    const copyToClipboardText = "{{ _('Copy to clipboard') }}";
</script>
<script src="{{ url_for('static', filename='js/gift_list.js') }}"></script>
<script src="{{ url_for('static', filename='js/clipboard.js') }}"></script>

<script>
        // Initialize tooltips and check for share parameter
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Check if the URL has a share parameter and open the share modal if it does
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('share')) {
            const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
            shareModal.show();
        }
    });

    // Gift deletion modal
    var confirmDeleteModal = document.getElementById('confirmDeleteModal');
    confirmDeleteModal.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        var giftId = button.getAttribute('data-id');
        var giftName = button.getAttribute('data-name');
        var deleteUrl = "{{ url_for('main.delete_gift', gift_id=0) }}".replace('0', giftId);

        this.querySelector('#confirmDeleteButton').setAttribute('href', deleteUrl);
        this.querySelector('#giftName').textContent = giftName;
    });

    // List deletion modal
    var confirmDeleteListModal = document.getElementById('confirmDeleteListModal');
    confirmDeleteListModal.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        var listId = button.getAttribute('data-id');
        var listName = button.getAttribute('data-name');
        var deleteUrl = "{{ url_for('main.delete_list', list_id=0) }}".replace('0', listId);

        this.querySelector('#confirmDeleteListForm').setAttribute('action', deleteUrl);
        this.querySelector('#listName').textContent = listName;
    });

    // Print invite function
    function printInvite() {
        var inviteMessage = document.getElementById('inviteMessage').value;
        var printWindow = window.open('', '', 'height=600,width=800');
        printWindow.document.write(`
            <html>
            <head>
                <title>Gift List Invite</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    h1 { color: #6366f1; }
                    .container { max-width: 500px; margin: 0 auto; }
                    .qr-code { margin: 30px 0; }
                    .message { font-size: 18px; line-height: 1.6; margin-bottom: 30px; }
                    .footer { margin-top: 40px; font-size: 14px; color: #6b7280; }

                    @media print {
                        body { padding: 0; }
                        .container { max-width: 100%; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Gift List Invitation</h1>
                    <div class="message">${inviteMessage}</div>
                    <div class="qr-code">
                        <img src="{{ url_for('main.qr_code', list_id=gift_list.unique_id) }}" alt="QR Code" width="200">
                    </div>
                    <p>Scan this QR code to view the gift list</p>
                    <p>Or visit: <strong>{{ url_for('main.gift_list_view', list_id=gift_list.unique_id, _external=True) }}{% if gift_list.is_private %}?code={{ gift_list.share_code }}{% endif %}</strong></p>
                    <div class="footer">Created with FiestaMagic</div>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        setTimeout(function () {
            printWindow.print();
        }, 500);
    }
</script>
{% endblock %}
